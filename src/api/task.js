import { spost, spostExport } from '@/request'
/**
 * 创建渗透测试任务
 * @param {Object} taskData - 任务数据
 * @param {string} taskData.departName - 部门名称
 * @param {string} taskData.staffName - 员工姓名
 * @param {string} taskData.sysName - 目标系统名称
 * @param {string} taskData.sysUrl - 目标系统URL
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const createTask = async (taskData, options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  return await spost(store, '/task/createTask', taskData, {
    showSuccessMessage: true,
    ...restOptions
  })
}

/**
 * 获取任务列表
 * @param {Object} params - 查询参数
 * @param {string} params.sysName - 系统名称（可选）
 * @param {string} params.status - 任务状态 0:未执行 1:执行中 9:已完成 -1:失败（可选）
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const getTaskList = async (params = {}, options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  return await spost(store, '/task/queryTaskList', params, {
    method: 'post',
    ...restOptions
  })
}

/**
 *  任务执行
 * @param {string} taskId - 任务ID
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const startTaskExecution = async (taskId, options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  return await spost(
    store,
    `/task/exec`,
    { taskId },
    {
      showSuccessMessage: false,
      ...restOptions
    }
  )
}

/**
 * 获取任务详情
 * @param {string} taskId - 任务ID
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const getTaskDetail = async (taskId, options = {}) => {
  console.log('getTaskDetail API 调用:', {
    taskId,
    taskIdType: typeof taskId,
    taskIdLength: taskId?.length
  })
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  const result = await spost(
    store,
    `/task/queryTaskDetail`,
    { taskId },
    {
      method: 'post',
      ...restOptions
    }
  )
  console.log('getTaskDetail API 响应:', result)
  return result
}

/**
 * 删除任务
 * @param {string} taskId - 任务ID
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const deleteTask = async (taskId, options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  return await spost(
    store,
    `/task/deleteTask`,
    { taskId },
    {
      showSuccessMessage: true,
      ...restOptions
    }
  )
}

/**
 * 终止任务执行
 * @param {string} taskId - 任务ID
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const terminateTask = async (taskId, options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}
  return await spost(
    store,
    `/task/killTask`,
    { taskId },
    {
      showSuccessMessage: true,
      ...restOptions
    }
  )
}

/**
 * 下载渗透测试报告
 * @param {string} taskId - 任务ID
 * @param {string} fileName - 自定义文件名（可选）
 * @param {Object} options - 请求选项
 * @returns {Promise<boolean>} 下载是否成功
 */
export const downloadTaskReport = async (taskId, fileName = '', options = {}) => {
  return await spostExport(`/task/downloadReport`, { taskId }, fileName, {
    loadingMessage: '正在下载渗透测试报告，请稍后...',
    successMessage: '报告下载成功',
    errorMessage: '报告下载失败',
    ...options
  })
}

/**
 * 上传预设漏洞文件
 * @param {File} file - 要上传的文件
 * @param {Object} options - 请求选项
 * @param {Object} options.store - 可选的 store 对象，如果不传则创建新的
 * @returns {Promise<Object>} 返回包含状态的 store 对象
 */
export const uploadVulnerabilityFile = async (file, options = {}) => {
  const { store: customStore, ...restOptions } = options
  const store = customStore || {}

  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', 'vulnerability')

  return await spost(store, '/file/upload', formData, {
    isFormData: true,
    showSuccessMessage: false,
    showErrorMessage: true,
    ...restOptions
  })
}
