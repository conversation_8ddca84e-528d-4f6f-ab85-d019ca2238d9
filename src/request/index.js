import axios from 'axios'
import loading from './loading'
import PromiseState from './promiseState'
import { setupInterceptors } from './interceptors'
import { initializeStore, handleSuccess, handleError } from './storeHandlers'
import { extractFileName, detectContentType, downloadFile, convertToFormData } from './fileHandler'

const defaultAPI = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 1000 * 30,
  headers: {
    'Content-Type': 'application/json'
  }
})
setupInterceptors(defaultAPI)
export function newStore() {
  return reactive(new PromiseState())
}

export async function spost(store, path, data, options = {}) {
  const {
    method = 'post',
    headers = {},
    timeout,
    isFormData = false,
    showSuccessMessage = false,
    showErrorMessage = true,
    cancelable = false,
    ...restOptions
  } = options
  initializeStore(store, path, data)

  try {
    const config = {
      headers: {
        ...headers,
        'Content-Type': isFormData ? 'multipart/form-data' : 'application/json'
      },
      cancelable,
      ...restOptions
    }

    if (timeout) {
      config.timeout = timeout
    }
    const res = await (method === 'get'
      ? defaultAPI.get(path, { params: data, ...config })
      : defaultAPI.post(path, isFormData ? convertToFormData(data) : data, config))
    handleSuccess(store, res, isFormData, showSuccessMessage, showErrorMessage)
    return store
  } catch (err) {
    if (err.code === 'REQUEST_CANCELED') {
      store.p = false
      store.o = false
      store.e = false
      store.c = 'CANCELED'
      store.d = {}
      store.m = '请求已取消'
      return store
    }
    handleError(store, err)
  }
}

export async function spostExport(path, data, customeFileName = '', options = {}) {
  const {
    method = 'post',
    loadingMessage = '正在下载，请稍后...',
    contentType = 'application/octet-stream',
    successMessage = '下载成功',
    errorMessage = '下载失败',
    showLoading = true,
    headers = {},
    ...restOptions
  } = options

  if (showLoading) {
    loading.open(loadingMessage, '#fff')
  }

  try {
    const config = {
      responseType: 'blob',
      headers,
      ...restOptions
    }

    const res = await (method === 'get'
      ? defaultAPI.get(path, { params: data, ...config })
      : defaultAPI.post(path, data, config))
    const fileName = extractFileName(res.headers['content-disposition'], customeFileName)
    const detectedContentType = detectContentType(fileName, contentType)
    const blob = new Blob([res.data], { type: detectedContentType })
    downloadFile(blob, customeFileName + decodeURIComponent(fileName))

    if (successMessage) {
      ElMessage.success({ message: successMessage })
    }

    return true
  } catch (error) {
    if (errorMessage) {
      ElMessage.error({ message: errorMessage })
    }
    throw error
  } finally {
    if (showLoading) {
      loading.close()
    }
  }
}
