/**
 * 开发服务器和预览服务器配置
 */

/**
 * 创建开发服务器配置
 * @param {Object} env 环境变量
 * @returns {Object} 服务器配置
 */
export function createServerConfig(env) {
  return {
    host: '0.0.0.0',
    port: 2025,
    open: true,
    proxy: {
      [env.VITE_API_BASE_URL]: {
        target: 'http://localhost:3000',
        // target: 'http://***********:8095',
        changeOrigin: true
      }
    }
  }
}

/**
 * 创建预览服务器配置
 * @returns {Object} 预览服务器配置
 */
export function createPreviewConfig() {
  return {
    port: 4173,
    host: '0.0.0.0',
    open: true
  }
}
