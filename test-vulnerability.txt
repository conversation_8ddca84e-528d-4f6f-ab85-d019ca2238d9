SQL注入漏洞测试文件

1. 用户登录页面SQL注入
   - 测试点：用户名输入框
   - 测试payload：admin' OR '1'='1
   - 预期结果：绕过登录验证

2. 搜索功能SQL注入
   - 测试点：搜索关键词输入框
   - 测试payload：'; DROP TABLE users; --
   - 预期结果：数据库表被删除

3. XSS跨站脚本攻击
   - 测试点：评论输入框
   - 测试payload：<script>alert('XSS')</script>
   - 预期结果：弹出警告框

4. 文件上传漏洞
   - 测试点：头像上传功能
   - 测试payload：上传.php后门文件
   - 预期结果：获得服务器权限

5. 目录遍历漏洞
   - 测试点：文件下载接口
   - 测试payload：../../../etc/passwd
   - 预期结果：读取系统敏感文件
